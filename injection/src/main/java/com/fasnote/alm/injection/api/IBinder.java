package com.fasnote.alm.injection.api;

/**
 * 依赖绑定器接口
 * 类似于Guice的Binder，用于配置服务绑定
 */
public interface IBinder {
    
    /**
     * 绑定接口到实现类
     * 
     * @param interfaceClass 接口类
     * @param implementationClass 实现类
     */
    <T> IBindingBuilder<T> bind(Class<T> interfaceClass, Class<? extends T> implementationClass);
    
    /**
     * 绑定接口到实例
     * 
     * @param interfaceClass 接口类
     * @param instance 实例
     */
    <T> void bind(Class<T> interfaceClass, T instance);
    
    /**
     * 绑定类到自身（用于具体类的注入）
     * 
     * @param clazz 类
     */
    <T> IBindingBuilder<T> bind(Class<T> clazz);
    
    /**
     * 注册服务提供者
     * 
     * @param provider 服务提供者
     */
    void registerProvider(IServiceProvider<?> provider);
    
    /**
     * 注册服务拦截器
     *
     * @param interceptor 拦截器
     */
    void registerInterceptor(IServiceInterceptor interceptor);

    /**
     * 注册服务解析器
     *
     * @param resolver 服务解析器
     */
    void registerServiceResolver(IServiceResolver resolver);

    /**
     * 注册服务实现类
     *
     * @param serviceClass 服务接口类
     * @param implementationClass 实现类
     */
    <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass);

    /**
     * 注册命名服务实现类
     *
     * @param serviceClass 服务接口类
     * @param implementationClass 实现类
     * @param name 服务名称
     * @param primary 是否为主要实现
     */
    <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass,
                                   String name, boolean primary);
}