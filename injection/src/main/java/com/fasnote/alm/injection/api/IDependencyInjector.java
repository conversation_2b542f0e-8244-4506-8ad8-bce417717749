package com.fasnote.alm.injection.api;

import java.util.List;
import java.util.Map;

/**
 * 依赖注入器接口
 * 提供依赖注入的核心功能，包括服务注册、实例创建和依赖解析
 * 
 * 注意：此接口专注于纯粹的依赖注入功能，通过模块化机制扩展
 */
public interface IDependencyInjector {
    
    /**
     * 安装模块
     * 
     * @param module 模块实例
     */
    void installModule(IModule module);
    
    /**
     * 安装多个模块
     * 
     * @param modules 模块列表
     */
    void installModules(IModule... modules);
    
    /**
     * 扫描并安装指定包下的模块
     * 
     * @param packageName 包名
     */
    void scanAndInstallModules(String packageName);
    
    /**
     * 获取已安装的模块列表
     * 
     * @return 模块列表
     */
    List<IModule> getInstalledModules();
    
    /**
     * 注册单例服务
     *
     * @param serviceClass 服务接口类
     * @param serviceInstance 服务实例
     */
    <T> void registerSingleton(Class<T> serviceClass, T serviceInstance);

    /**
     * 注册命名单例服务
     *
     * @param serviceClass 服务接口类
     * @param serviceInstance 服务实例
     * @param name 服务名称
     */
    <T> void registerSingleton(Class<T> serviceClass, T serviceInstance, String name);
    
    /**
     * 注册服务实现类
     * 
     * @param serviceClass 服务接口类
     * @param implementationClass 实现类
     */
    <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass);
    
    /**
     * 注册命名服务实现类
     *
     * @param serviceClass 服务接口类
     * @param implementationClass 实现类
     * @param name 服务名称
     * @param primary 是否为主要实现
     */
    <T> void registerImplementation(Class<T> serviceClass, Class<? extends T> implementationClass,
                                   String name, boolean primary);

    /**
     * 注册单例服务实现类（延迟初始化）
     *
     * @param serviceClass 服务接口类
     * @param implementationClass 实现类
     * @param name 服务名称
     * @param primary 是否为主要实现
     */
    <T> void registerSingletonImplementation(Class<T> serviceClass, Class<? extends T> implementationClass,
                                            String name, boolean primary);
    
    /**
     * 获取服务实例
     * 
     * @param serviceClass 服务类
     * @return 服务实例
     */
    <T> T getService(Class<T> serviceClass);
    
    /**
     * 获取命名服务实例
     *
     * @param serviceClass 服务类
     * @param name 服务名称
     * @return 服务实例
     */
    <T> T getService(Class<T> serviceClass, String name);

    /**
     * 基于字符串接口名称获取服务实例
     * 支持动态服务查找，主要用于许可证验证系统
     *
     * @param interfaceName 接口全限定名称
     * @return 服务实例，如果未找到返回null
     */
    Object getServiceByName(String interfaceName);

    /**
     * 基于字符串接口名称获取命名服务实例
     *
     * @param interfaceName 接口全限定名称
     * @param serviceName 服务名称
     * @return 服务实例，如果未找到返回null
     */
    Object getServiceByName(String interfaceName, String serviceName);

    /**
     * 直接通过服务名称获取服务实例
     * 这个方法会在所有已注册的命名服务中查找匹配的服务名称
     *
     * @param serviceName 服务名称
     * @return 服务实例，如果未找到返回null
     */
    Object getServiceByServiceName(String serviceName);
    
    /**
     * 获取所有实现
     * 
     * @param serviceClass 服务接口类
     * @return 所有实现实例的列表
     */
    <T> List<T> getAllImplementations(Class<T> serviceClass);
    
    /**
     * 创建对象并注入依赖
     * 
     * @param clazz 类
     * @return 注入依赖后的对象
     */
    <T> T createInstance(Class<T> clazz) throws Exception;
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getStatistics();
    
    /**
     * 清理所有注册的服务
     */
    void clear();

    /**
     * 获取服务接口的所有实现类
     *
     * @param serviceInterface 服务接口类
     * @return 实现类列表
     */
    java.util.List<Class<?>> getServiceImplementations(Class<?> serviceInterface);

    /**
     * 注册许可证验证器
     *
     * @param validator 许可证验证器
     */
    void registerLicenseValidator(ILicenseValidator validator);

    /**
     * 注册服务验证拦截器
     *
     * @param interceptor 服务验证拦截器
     */
    void registerValidationInterceptor(IServiceValidationInterceptor interceptor);

    /**
     * 注册服务解析器
     *
     * @param resolver 服务解析器
     */
    void registerServiceResolver(IServiceResolver resolver);

    // ==================== OSGi Bundle 管理 ====================

    /**
     * 注册OSGi Bundle以支持跨Bundle类扫描
     *
     * @param bundle OSGi Bundle实例
     */
    void registerBundle(org.osgi.framework.Bundle bundle);

    /**
     * 注销OSGi Bundle
     *
     * @param bundle OSGi Bundle实例
     */
    void unregisterBundle(org.osgi.framework.Bundle bundle);

    /**
     * 批量注册OSGi Bundle
     *
     * @param bundles Bundle数组
     */
    default void registerBundles(org.osgi.framework.Bundle... bundles) {
        for (org.osgi.framework.Bundle bundle : bundles) {
            registerBundle(bundle);
        }
    }

    /**
     * 批量注销OSGi Bundle
     *
     * @param bundles Bundle数组
     */
    default void unregisterBundles(org.osgi.framework.Bundle... bundles) {
        for (org.osgi.framework.Bundle bundle : bundles) {
            unregisterBundle(bundle);
        }
    }
}