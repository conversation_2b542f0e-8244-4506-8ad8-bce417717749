package com.fasnote.alm.plugin.manage.injection.resolver;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.annotations.Service;
import com.fasnote.alm.injection.api.IDependencyInjector;
import com.fasnote.alm.injection.api.IServiceResolver;
import com.fasnote.alm.injection.facade.DI;
import com.fasnote.alm.plugin.manage.annotation.FallbackImplementation;
import com.fasnote.alm.plugin.manage.annotation.LicenseImplementation;
import com.fasnote.alm.plugin.manage.core.LicenseManager;

/**
 * 许可证感知的服务解析器
 * 
 * 负责根据许可证状态和优先级选择合适的服务实现：
 * 1. 许可证验证通过时，优先使用 @LicenseImplementation 标记的实现
 * 2. 许可证验证失败时，使用 @FallbackImplementation 标记的实现
 * 3. 支持优先级排序和命名服务
 */
public class LicenseAwareServiceResolver implements IServiceResolver {

    private static final Logger logger = LoggerFactory.getLogger(LicenseAwareServiceResolver.class);
    
    // 缓存接口的所有实现类信息
    private final Map<Class<?>, List<ServiceImplementationInfo>> implementationCache = new ConcurrentHashMap<>();

    private final LicenseManager licenseManager;
    private final IDependencyInjector dependencyInjector;

    public LicenseAwareServiceResolver(LicenseManager licenseManager) {
        this.licenseManager = licenseManager;
        this.dependencyInjector = DI.getInjector();
        if (this.dependencyInjector == null) {
            logger.warn("无法获取DependencyInjector实例，自动实现发现功能将不可用");
        }
    }

    /**
     * 构造函数（用于测试）
     */
    public LicenseAwareServiceResolver(LicenseManager licenseManager, IDependencyInjector dependencyInjector) {
        this.licenseManager = licenseManager;
        this.dependencyInjector = dependencyInjector;
    }
    
    @Override
    public boolean shouldResolve(Class<?> serviceClass) {
        // 检查是否有许可证相关注解的实现类
        return hasLicenseRelatedImplementations(serviceClass);
    }
    
    @Override
    public Class<?> resolveImplementation(Class<?> serviceClass, String serviceName, Class<?> defaultImplementation) {
        try {
            // 获取接口的所有实现
            List<ServiceImplementationInfo> implementations = getImplementations(serviceClass);
            if (implementations.isEmpty()) {
                return defaultImplementation; // 使用默认实现
            }
            
            // 根据许可证状态选择合适的实现
            Class<?> selectedImplementation = selectImplementationByLicense(serviceClass, implementations);
            if (selectedImplementation != null) {
                logger.debug("许可证感知服务选择: {} -> {}", serviceClass.getName(), selectedImplementation.getName());
                return selectedImplementation;
            }
            
        } catch (Exception e) {
            logger.error("许可证感知服务选择失败: {}", serviceClass.getName(), e);
        }
        
        return defaultImplementation; // 使用默认实现
    }
    
    @Override
    public int getPriority() {
        return 10; // 高优先级，确保在其他解析器之前执行
    }
    
    /**
     * 检查接口是否有许可证相关的实现类（自动发现）
     */
    private boolean hasLicenseRelatedImplementations(Class<?> serviceInterface) {
        // 首先检查缓存
        if (implementationCache.containsKey(serviceInterface)) {
            List<ServiceImplementationInfo> cached = implementationCache.get(serviceInterface);
            return cached != null && !cached.isEmpty();
        }

        // 自动从DI容器发现实现类
        return autoDiscoverImplementations(serviceInterface);
    }

    /**
     * 自动从DI容器发现接口的实现类
     */
    private boolean autoDiscoverImplementations(Class<?> serviceInterface) {
        if (dependencyInjector == null) {
            logger.debug("DependencyInjector不可用，跳过自动发现: {}", serviceInterface.getName());
            return false;
        }

        try {
            // 从DI容器获取所有实现类
            List<Class<?>> implementations = dependencyInjector.getServiceImplementations(serviceInterface);

            if (implementations.isEmpty()) {
                logger.debug("未发现接口实现: {}", serviceInterface.getName());
                return false;
            }

            logger.debug("自动发现接口实现: {} -> {} 个实现", serviceInterface.getName(), implementations.size());

            // 转换为ServiceImplementationInfo并缓存
            List<ServiceImplementationInfo> implementationInfos = new ArrayList<>();
            boolean hasLicenseRelated = false;

            for (Class<?> implClass : implementations) {
                ServiceImplementationInfo info = createImplementationInfo(implClass);
                if (info != null) {
                    implementationInfos.add(info);
                    // 检查是否有许可证相关注解
                    if (info.isLicenseImplementation() || info.isFallbackImplementation()) {
                        hasLicenseRelated = true;
                    }
                    logger.debug("发现实现类: {} -> {}", serviceInterface.getName(), info);
                }
            }

            // 缓存结果
            if (!implementationInfos.isEmpty()) {
                implementationCache.put(serviceInterface, implementationInfos);
            }

            return hasLicenseRelated;

        } catch (Exception e) {
            logger.warn("自动发现实现类失败: {} - {}", serviceInterface.getName(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取接口的所有实现类信息（使用自动发现的缓存）
     */
    private List<ServiceImplementationInfo> getImplementations(Class<?> serviceInterface) {
        // 首先尝试从缓存获取
        List<ServiceImplementationInfo> cached = implementationCache.get(serviceInterface);
        if (cached != null) {
            return cached;
        }

        // 如果缓存中没有，触发自动发现
        if (autoDiscoverImplementations(serviceInterface)) {
            cached = implementationCache.get(serviceInterface);
            if (cached != null) {
                return cached;
            }
        }

        // 如果自动发现失败，返回空列表
        logger.debug("未找到接口实现: {}", serviceInterface.getName());
        return new ArrayList<>();
    }
    
    /**
     * 根据许可证状态选择合适的实现
     */
    private Class<?> selectImplementationByLicense(Class<?> serviceInterface, List<ServiceImplementationInfo> implementations) {
        // 按优先级排序
        implementations.sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()));

        // 检查开发模式
        if (isDevelopmentMode()) {
            logger.debug("开发模式下，优先选择许可证核心实现: {}", serviceInterface.getName());
            // 开发模式下直接选择许可证核心实现，不进行许可证验证
            for (ServiceImplementationInfo impl : implementations) {
                if (impl.isLicenseImplementation()) {
                    logger.debug("开发模式选择许可证核心实现: {} (优先级: {})", impl.getImplementationClass().getName(), impl.getPriority());
                    return impl.getImplementationClass();
                }
            }
            // 开发模式下如果没有许可证核心实现，也选择回退基础实现
            for (ServiceImplementationInfo impl : implementations) {
                if (impl.isFallbackImplementation()) {
                    logger.debug("开发模式选择回退基础实现: {} (优先级: {})", impl.getImplementationClass().getName(), impl.getPriority());
                    return impl.getImplementationClass();
                }
            }
        }

        // 首先尝试许可证核心实现
        for (ServiceImplementationInfo impl : implementations) {
            if (impl.isLicenseImplementation()) {
                // 检查许可证是否有效
                if (licenseManager != null && isLicenseValid(serviceInterface, impl.getImplementationClass())) {
                    logger.debug("选择许可证核心实现: {} (优先级: {})", impl.getImplementationClass().getName(), impl.getPriority());
                    return impl.getImplementationClass();
                }
            }
        }
        
        // 如果没有有效的许可证实现，选择回退基础实现
        for (ServiceImplementationInfo impl : implementations) {
            if (impl.isFallbackImplementation()) {
                logger.debug("选择回退基础实现: {} (优先级: {})", impl.getImplementationClass().getName(), impl.getPriority());
                return impl.getImplementationClass();
            }
        }

        // 如果没有找到任何标记的实现，返回null让DI容器处理
        logger.warn("未找到许可证核心实现或回退基础实现: {}", serviceInterface.getName());
        return null;
    }
    
    /**
     * 检查许可证是否有效
     */
    private boolean isLicenseValid(Class<?> serviceInterface, Class<?> implementationClass) {
        try {
            // 使用现有的许可证管理器进行验证
            Object instance = licenseManager.createServiceInstanceFromLicense(serviceInterface);
            return instance != null && implementationClass.isInstance(instance);
        } catch (Exception e) {
            logger.debug("许可证验证失败: {}", implementationClass.getName(), e);
            return false;
        }
    }

    /**
     * 检查是否为开发模式
     */
    private boolean isDevelopmentMode() {
        // 检查系统属性
        String developmentMode = System.getProperty("license.development.mode", "false");
        String testMode = System.getProperty("license.test.mode", "false");

        boolean isDev = "true".equalsIgnoreCase(developmentMode) || "true".equalsIgnoreCase(testMode);

        if (isDev) {
            logger.debug("检测到开发/测试模式，将跳过许可证验证");
        }

        return isDev;
    }
    
    /**
     * 手动注册实现类信息到缓存（可选）
     */
    public void registerImplementation(Class<?> serviceInterface, Class<?> implementationClass) {
        ServiceImplementationInfo info = createImplementationInfo(implementationClass);
        if (info != null) {
            implementationCache.computeIfAbsent(serviceInterface, k -> new ArrayList<>()).add(info);
            logger.debug("手动注册实现类信息: {} -> {}", serviceInterface.getName(), implementationClass.getName());
        }
    }

    /**
     * 清理实现类缓存
     */
    public void clearImplementationCache(Class<?> serviceInterface) {
        if (serviceInterface != null) {
            implementationCache.remove(serviceInterface);
            logger.debug("清理服务接口缓存: {}", serviceInterface.getName());
        } else {
            implementationCache.clear();
            logger.debug("清理所有实现类缓存");
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStatistics() {
        int totalInterfaces = implementationCache.size();
        int totalImplementations = implementationCache.values().stream()
                                                     .mapToInt(List::size)
                                                     .sum();
        return String.format("缓存统计: %d个接口, %d个实现类", totalInterfaces, totalImplementations);
    }

    /**
     * 创建实现类信息
     */
    private ServiceImplementationInfo createImplementationInfo(Class<?> implementationClass) {
        // 检查是否有许可证相关注解
        if (!hasLicenseAnnotation(implementationClass)) {
            // 检查是否有直接的 @Service 注解
            Service serviceAnnotation = implementationClass.getAnnotation(Service.class);
            if (serviceAnnotation == null) {
                return null; // 不是服务类
            }

            // 创建普通服务信息
            return new ServiceImplementationInfo(implementationClass, serviceAnnotation.priority(), false, false);
        }

        // 提取许可证注解配置
        ServiceConfig config = extractServiceConfig(implementationClass);
        if (config == null) {
            return null;
        }

        return new ServiceImplementationInfo(
            config.getImplementationClass(),
            config.getPriority(),
            config.isLicenseImplementation(),
            config.isFallbackImplementation()
        );
    }

    /**
     * 检查类是否有许可证相关注解
     */
    private boolean hasLicenseAnnotation(Class<?> clazz) {
        return clazz.isAnnotationPresent(LicenseImplementation.class) ||
               clazz.isAnnotationPresent(FallbackImplementation.class);
    }

    /**
     * 从许可证注解中提取服务配置信息
     */
    private ServiceConfig extractServiceConfig(Class<?> clazz) {
        if (clazz.isAnnotationPresent(LicenseImplementation.class)) {
            return extractFromLicenseImplementation(clazz);
        } else if (clazz.isAnnotationPresent(FallbackImplementation.class)) {
            return extractFromFallbackImplementation(clazz);
        }
        return null;
    }

    /**
     * 从 @LicenseImplementation 注解提取服务配置
     */
    private ServiceConfig extractFromLicenseImplementation(Class<?> clazz) {
        LicenseImplementation annotation = clazz.getAnnotation(LicenseImplementation.class);

        ServiceConfig config = new ServiceConfig();
        config.setImplementationClass(clazz);
        config.setPriority(50); // 许可证实现默认高优先级
        config.setLicenseImplementation(true);

        // 提取服务接口
        Class<?>[] interfaces = annotation.interfaces();
        if (interfaces.length > 0) {
            config.setInterfaces(interfaces);
        } else {
            // 自动推断接口
            Class<?>[] inferredInterfaces = clazz.getInterfaces();
            if (inferredInterfaces.length > 0) {
                config.setInterfaces(new Class<?>[]{inferredInterfaces[0]});
            }
        }

        // 提取服务名称
        String serviceName = annotation.serviceName();
        if (!serviceName.isEmpty()) {
            config.setServiceName(serviceName);
        }

        logger.debug("从 @LicenseImplementation 提取服务配置: {}", config);
        return config;
    }

    /**
     * 从 @FallbackImplementation 注解提取服务配置
     */
    private ServiceConfig extractFromFallbackImplementation(Class<?> clazz) {
        FallbackImplementation annotation = clazz.getAnnotation(FallbackImplementation.class);

        ServiceConfig config = new ServiceConfig();
        config.setImplementationClass(clazz);
        config.setPriority(annotation.priority());
        config.setFallbackImplementation(true);

        // 提取服务接口
        Class<?> serviceInterface = annotation.value();
        if (serviceInterface != null) {
            config.setInterfaces(new Class<?>[]{serviceInterface});
        } else {
            // 自动推断接口
            Class<?>[] inferredInterfaces = clazz.getInterfaces();
            if (inferredInterfaces.length > 0) {
                config.setInterfaces(new Class<?>[]{inferredInterfaces[0]});
            }
        }

        logger.debug("从 @FallbackImplementation 提取服务配置: {}", config);
        return config;
    }
    
    /**
     * 服务实现信息
     */
    private static class ServiceImplementationInfo {
        private final Class<?> implementationClass;
        private final int priority;
        private final boolean isLicenseImplementation;
        private final boolean isFallbackImplementation;

        public ServiceImplementationInfo(Class<?> implementationClass, int priority,
                                       boolean isLicenseImplementation, boolean isFallbackImplementation) {
            this.implementationClass = implementationClass;
            this.priority = priority;
            this.isLicenseImplementation = isLicenseImplementation;
            this.isFallbackImplementation = isFallbackImplementation;
        }

        public Class<?> getImplementationClass() { return implementationClass; }
        public int getPriority() { return priority; }
        public boolean isLicenseImplementation() { return isLicenseImplementation; }
        public boolean isFallbackImplementation() { return isFallbackImplementation; }
        
        @Override
        public String toString() {
            return String.format("ServiceImplementationInfo{class=%s, priority=%d, license=%s, fallback=%s}",
                               implementationClass.getSimpleName(), priority, isLicenseImplementation, isFallbackImplementation);
        }
    }

    /**
     * 服务配置信息
     */
    private static class ServiceConfig {
        private Class<?> implementationClass;
        private Class<?>[] interfaces = new Class<?>[0];
        private String serviceName = "";
        private int priority = 100;
        private boolean isLicenseImplementation = false;
        private boolean isFallbackImplementation = false;

        public Class<?> getImplementationClass() { return implementationClass; }
        public void setImplementationClass(Class<?> implementationClass) { this.implementationClass = implementationClass; }

        public Class<?>[] getInterfaces() { return interfaces; }
        public void setInterfaces(Class<?>[] interfaces) { this.interfaces = interfaces; }

        public String getServiceName() { return serviceName; }
        public void setServiceName(String serviceName) { this.serviceName = serviceName; }

        public int getPriority() { return priority; }
        public void setPriority(int priority) { this.priority = priority; }

        public boolean isLicenseImplementation() { return isLicenseImplementation; }
        public void setLicenseImplementation(boolean licenseImplementation) { this.isLicenseImplementation = licenseImplementation; }

        public boolean isFallbackImplementation() { return isFallbackImplementation; }
        public void setFallbackImplementation(boolean fallbackImplementation) { this.isFallbackImplementation = fallbackImplementation; }
        
        @Override
        public String toString() {
            return String.format("ServiceConfig{class=%s, priority=%d, license=%s, fallback=%s}",
                               implementationClass != null ? implementationClass.getSimpleName() : "null", 
                               priority, isLicenseImplementation, isFallbackImplementation);
        }
    }
}
