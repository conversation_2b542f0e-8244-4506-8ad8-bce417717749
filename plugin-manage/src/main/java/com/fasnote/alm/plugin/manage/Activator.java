package com.fasnote.alm.plugin.manage;

import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.osgi.framework.ServiceRegistration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IPackageScanProvider;
import com.fasnote.alm.plugin.manage.config.LicenseProperties;
import com.fasnote.alm.plugin.manage.injection.module.LicensePackageScanProvider;
import com.fasnote.alm.plugin.manage.repository.LicenseRepository;
import com.fasnote.alm.plugin.manage.repository.impl.InMemoryLicenseRepository;
import com.fasnote.alm.plugin.manage.security.SecurityValidator;
import com.fasnote.alm.plugin.manage.service.LicenseService;
import com.fasnote.alm.plugin.manage.service.impl.LicenseServiceImpl;

/**
 * 许可证管理插件激活器 负责插件的启动和停止，初始化核心服务
 */
public class Activator implements BundleActivator {

	private static final Logger logger = LoggerFactory.getLogger(Activator.class);

	private static BundleContext context;
	private static LicenseService licenseService;
	private static LicenseRepository licenseRepository;
	private static SecurityValidator securityValidator;
	private static LicenseProperties licenseProperties;

	// OSGi 服务注册
	private ServiceRegistration<IPackageScanProvider> packageScanProviderRegistration;

	/**
	 * 获取Bundle上下文
	 */
	public static BundleContext getContext() {
		return context;
	}

	/**
	 * 获取许可证配置
	 */
	public static LicenseProperties getLicenseProperties() {
		return licenseProperties;
	}

	/**
	 * 获取许可证存储库
	 */
	public static LicenseRepository getLicenseRepository() {
		return licenseRepository;
	}

	/**
	 * 获取许可证服务
	 */
	public static LicenseService getLicenseService() {
		return licenseService;
	}

	/**
	 * 获取安全验证器
	 */
	public static SecurityValidator getSecurityValidator() {
		return securityValidator;
	}

	/**
	 * 清理资源
	 */
	private void cleanup() {
		logger.debug("清理资源...");

		// 清理存储库
		if (licenseRepository instanceof InMemoryLicenseRepository) {
			((InMemoryLicenseRepository) licenseRepository).clear();
		}

		logger.debug("资源清理完成");
	}

	/**
	 * 初始化核心组件
	 */
	private void initializeComponents() {
		logger.debug("初始化核心组件...");

		// 初始化安全验证器
		securityValidator = new SecurityValidator();
		try {
			securityValidator.initialize();
		} catch (Exception e) {
			logger.error("SecurityValidator 初始化失败", e);
		}

		// 初始化许可证存储库
		licenseRepository = new InMemoryLicenseRepository();

		logger.debug("核心组件初始化完成");
	}

	/**
	 * 初始化配置
	 */
	private void initializeConfiguration() {
		logger.debug("初始化许可证配置...");

		licenseProperties = new LicenseProperties();

		// 从系统属性或环境变量加载配置
		loadConfigurationFromEnvironment();

		logger.debug("许可证配置初始化完成");
	}

	/**
	 * 初始化服务
	 */
	private void initializeServices() {
		logger.debug("初始化服务...");

		// 初始化许可证服务
		licenseService = new LicenseServiceImpl(licenseRepository, securityValidator);

		logger.debug("服务初始化完成");
	}

	/**
	 * 从环境变量加载配置
	 */
	private void loadConfigurationFromEnvironment() {
		// 存储目录配置
		String storageDir = System.getProperty("license.storage.directory");
		if (storageDir != null) {
			licenseProperties.setStorageDirectory(storageDir);
		}

		// 缓存配置
		String cacheSize = System.getProperty("license.cache.size");
		if (cacheSize != null) {
			try {
				licenseProperties.setCacheSize(Integer.parseInt(cacheSize));
			} catch (NumberFormatException e) {
				logger.warn("无效的缓存大小配置: " + cacheSize);
			}
		}

		// 安全配置
		String validationEnabled = System.getProperty("security.validation.enabled");
		if (validationEnabled != null) {
			licenseProperties.setValidationEnabled(Boolean.parseBoolean(validationEnabled));
		}

		logger.debug("从环境变量加载配置完成");
	}

	/**
	 * 注册包扫描提供者为 OSGi 服务
	 */
	private void registerPackageScanProvider() {
		try {
			logger.info("注册 LicensePackageScanProvider 为 OSGi 服务...");

			// 创建包扫描提供者实例
			IPackageScanProvider packageScanProvider = new LicensePackageScanProvider();

			// 注册为 OSGi 服务
			packageScanProviderRegistration = context.registerService(
				IPackageScanProvider.class,
				packageScanProvider,
				null
			);

			logger.info("LicensePackageScanProvider OSGi 服务注册成功");
			logger.info("扫描包路径: {}", java.util.Arrays.toString(packageScanProvider.getScanPackages()));

		} catch (Exception e) {
			logger.error("注册 LicensePackageScanProvider OSGi 服务失败", e);
		}
	}

	@Override
	public void start(BundleContext bundleContext) throws Exception {
		logger.info("启动许可证管理插件...");

		try {
			Activator.context = bundleContext;

			// 初始化配置
			initializeConfiguration();

			// 初始化核心组件
			initializeComponents();

			// 初始化服务
			initializeServices();

			// 注册包扫描提供者
			registerPackageScanProvider();

			logger.info("许可证管理插件启动成功");

		} catch (Exception e) {
			logger.error("许可证管理插件启动失败", e);
			throw e;
		}
	}

	@Override
	public void stop(BundleContext bundleContext) throws Exception {
		logger.info("停止许可证管理插件...");

		try {
			// 注销包扫描提供者服务
			if (packageScanProviderRegistration != null) {
				packageScanProviderRegistration.unregister();
				packageScanProviderRegistration = null;
				logger.info("LicensePackageScanProvider OSGi 服务注销成功");
			}

			// 清理资源
			cleanup();

			// OSGi许可证框架现在通过DI自动管理，无需手动清理

			// 清空静态引用
			Activator.context = null;
			licenseService = null;
			licenseRepository = null;
			securityValidator = null;
			licenseProperties = null;

			logger.info("许可证管理插件停止成功");

		} catch (Exception e) {
			logger.error("许可证管理插件停止失败", e);
			throw e;
		}
	}
}
